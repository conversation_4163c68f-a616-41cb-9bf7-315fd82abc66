# 邮智后端代码

* Dockerfile 使用 poetry 进行 Python 依赖管理

## 本地源码启动方式

### 新增后端配置文件

在`src/backend/bisheng`下新增`config.yaml`配置文件，修改其中的mysql、redis、milvus、es及minio的配置

如修改为`*************`的组件配置：
```yaml
# 数据库配置
database_url:
  "mysql+pymysql://root:gAAAAABlp4b4c59FeVGF_OQRVf6NOUIGdxq8246EBD-b0hdK_jVKRs1x4PoAn0A6C5S6IiFKmWn0Nm5eBUWu-7jxcqw6TiVjQA==@*************:3306/bisheng?charset=utf8mb4"

# 缓存配置
redis_url: "redis://*************:6379/1"

# celery的broken地址
celery_redis_url: "redis://*************:6379/2"

# 知识库的milvus和es配置
vector_stores:
  milvus:
    connection_args: '{"host":"*************","port":"19530","user":"","password":"","secure":false}'
    is_partition: 'true'
    partition_suffix: "1"
  elasticsearch:
    url: "http://*************:9200"
    ssl_verify: '{}'

# 对象存储
object_storage:
  type: minio
  minio:
    schema: 'false'
    cert_check: 'false'
    endpoint: "*************:9000"
    sharepoint: "*************:9000"
    access_key: "minioadmin"
    secret_key: "minioadmin"
```

### 启动后端代码

进入`src/backend`目录，通过`uvicorn`启动后端代码
```bash
poetry run uvicorn bisheng.main:app --host 0.0.0.0 --port 7860 
```

以及 `celery client` 也需要启动。

```bash
poetry run celery -A bisheng.worker.main worker -l info --pool=solo
```

## 部署docker报错。
如果显示：
```bash
from bisheng.workflow.nodes.output.output_fake import OutputFakeNode
ModuleNotFoundError: No module named 'bisheng.workflow.nodes.output'
```
则需要手动创建`output`文件夹，并添加`output_fake.py`文件。如果是服务器上使用 git 命令拉取的仓库代码，则需要在其他地方，将这个 output 文件夹复制到对应位置：
`PS D:\projects\post-wise\postwise\src\backend\bisheng\workflow\nodes\output>`

- 2025/8/8: 
1. 如果不想执行上述的方法，可以直接去BISHENG官网的v1.0.1版本，下载源码，然后直接将output文件夹复制到对应位置即可。
2. 注意路径是：from bisheng.workflow.nodes.output.output （更新为版本 v1.0.1.3) 

- 2025/8/12:
这个问题是 `gitignore` 文件导致的，将 output 文件夹添加到 .gitignore 文件中，就不会被 git 忽略。
