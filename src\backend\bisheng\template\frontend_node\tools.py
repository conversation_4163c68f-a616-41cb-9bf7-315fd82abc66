from bisheng.template.field.base import Template<PERSON><PERSON>
from bisheng.template.frontend_node.base import FrontendNode
from bisheng.template.template.base import Template
from bisheng.utils.constants import DEFAULT_PYTHON_FUNCTION


class ToolNode(FrontendNode):
    name: str = 'Tool'
    template: Template = Template(
        type_name='Tool',
        fields=[
            TemplateField(
                field_type='str',
                required=True,
                placeholder='',
                is_list=False,
                show=True,
                multiline=True,
                value='',
                name='name',
                advanced=False,
            ),
            TemplateField(
                field_type='str',
                required=True,
                placeholder='',
                is_list=False,
                show=True,
                multiline=True,
                value='',
                name='description',
                advanced=False,
            ),
            TemplateField(
                name='func',
                field_type='function',
                required=True,
                is_list=False,
                show=True,
                multiline=True,
                advanced=False,
            ),
            TemplateField(
                field_type='bool',
                required=True,
                placeholder='',
                is_list=False,
                show=True,
                multiline=False,
                value=False,
                name='return_direct',
            ),
            Template<PERSON>ield(
                field_type='NestedDict',
                required=True,
                placeholder='',
                show=True,
                multiline=True,
                value='{"arg1": {"type": "string"}}',
                name='args_schema',
            ),
        ],
    )
    description: str = 'Converts a chain, agent or function into a tool.'
    base_classes: list[str] = ['Tool', 'BaseTool']

    def to_dict(self):
        return super().to_dict()


class PythonFunctionToolNode(FrontendNode):
    name: str = 'PythonFunctionTool'
    template: Template = Template(
        type_name='PythonFunctionTool',
        fields=[
            TemplateField(
                field_type='str',
                required=True,
                placeholder='',
                is_list=False,
                show=True,
                multiline=False,
                value='',
                name='name',
                advanced=False,
            ),
            TemplateField(
                field_type='str',
                required=True,
                placeholder='',
                is_list=False,
                show=True,
                multiline=False,
                value='',
                name='description',
                advanced=False,
            ),
            TemplateField(
                field_type='code',
                required=True,
                placeholder='',
                is_list=False,
                show=True,
                value=DEFAULT_PYTHON_FUNCTION,
                name='code',
                advanced=False,
            ),
            TemplateField(
                field_type='bool',
                required=True,
                placeholder='',
                is_list=False,
                show=True,
                multiline=False,
                value=False,
                name='return_direct',
            ),
        ],
    )
    description: str = 'Python function to be executed.'
    base_classes: list[str] = ['BaseTool', 'Tool']

    def to_dict(self):
        return super().to_dict()


class PythonFunctionNode(FrontendNode):
    name: str = 'PythonFunction'
    template: Template = Template(
        type_name='PythonFunction',
        fields=[
            TemplateField(
                field_type='code',
                required=True,
                placeholder='',
                is_list=False,
                show=True,
                value=DEFAULT_PYTHON_FUNCTION,
                name='code',
                advanced=False,
            )
        ],
    )
    description: str = 'Python function to be executed.'
    base_classes: list[str] = ['function']

    def to_dict(self):
        return super().to_dict()
