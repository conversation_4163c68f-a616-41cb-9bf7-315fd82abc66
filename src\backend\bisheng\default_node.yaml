autogen_roles:
  AutoGenAssistant:
    documentation: ""
  AutoGenGroupChatManager:
    documentation: ""
  AutoGenUser:
    documentation: ""
  AutoGenCoder:
    documentation: ""
  AutoGenCustomRole:
    documentation: ""
agents:
  LLMFunctionsAgent:
    documentation: ""
  ZeroShotAgent:
    documentation: "https://python.langchain.com/docs/modules/agents/how_to/custom_mrkl_agent"
  JsonAgent:
    documentation: "https://python.langchain.com/docs/modules/agents/toolkits/openapi"
  CSVAgent:
    documentation: "https://python.langchain.com/docs/modules/agents/toolkits/csv"
  AgentInitializer:
    documentation: "https://python.langchain.com/docs/modules/agents/agent_types/"
  VectorStoreAgent:
    documentation: ""
  VectorStoreRouterAgent:
    documentation: ""
  SQLAgent:
    documentation: ""
  ChatglmFunctionsAgent:
    documentation: ""
  OpenAIToolsAgent:
    documentation: ""
chains:
  RetrievalChain:
    documentation: ""
  RuleBasedRouter:
    documentation: ""
  MultiRuleChain:
    documentation: ""
  MultiPromptChain:
    documentation: ""
  LLMRouterChain:
    documentation: ""
  AutoGenChain:
    documentation: ""
  APIChain:
    documentation: ""
  TransformChain:
    documentation: ""
  QAGenerationChainV2:
    documentation: ""
  SimpleSequentialChain:
    documentation: ""
  SequentialChain:
    documentation: ""
  LLMChain:
    documentation: "https://python.langchain.com/docs/modules/chains/foundational/llm_chain"
  LLMMathChain:
    documentation: "https://python.langchain.com/docs/modules/chains/additional/llm_math"
  LLMCheckerChain:
    documentation: "https://python.langchain.com/docs/modules/chains/additional/llm_checker"
  ConversationChain:
    documentation: ""
  SeriesCharacterChain:
    documentation: ""
  MidJourneyPromptChain:
    documentation: ""
  TimeTravelGuideChain:
    documentation: ""
  SQLDatabaseChain:
    documentation: ""
  RetrievalQA:
    documentation: "https://python.langchain.com/docs/modules/chains/popular/vector_db_qa"
  BishengRetrievalQA:
    documentation: ""
  RetrievalQAWithSourcesChain:
    documentation: ""
  ConversationalRetrievalChain:
    documentation: "https://python.langchain.com/docs/modules/chains/popular/chat_vector_db"
  CombineDocsChain:
    documentation: ""
  # SummarizeDocsChain:
  #   documentation: ""
  LoaderOutputChain:
    documentation: ""
  DalleGeneratorChain:
    documentation: ""
documentloaders:
  FireCrawlLoader:
    documentation: ""
  CustomKVLoader:
    documentation: ""
  UniversalKVLoader:
    documentation: ""
  ElemUnstructuredLoaderV0:
    documentation: ""
  AirbyteJSONLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/airbyte_json"
  CoNLLULoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/conll-u"
  CSVLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/csv"
  EverNoteLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/evernote"
  FacebookChatLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/facebook_chat"
  GutenbergLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/gutenberg"
  BSHTMLLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/how_to/html"
  PyPDFDirectoryLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/how_to/pdf"
  PyPDFLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/how_to/pdf"
  SRTLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/subtitle"
  TelegramChatLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/telegram"
  TextLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/"
  WebBaseLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/web_base"
  AZLyricsLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/azlyrics"
  CollegeConfidentialLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/college_confidential"
  HNLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/hacker_news"
  IFixitLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/ifixit"
  IMSDbLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/imsdb"
  GitbookLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/gitbook"
  ReadTheDocsLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/readthedocs_documentation"
  SlackDirectoryLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/slack"
  NotionDirectoryLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/notion"
  DirectoryLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/how_to/file_directory"
  GitLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/git"
  PDFWithSemanticLoader:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_loaders/integrations/git"
embeddings:
  BishengEmbedding:
    documentation: ""
  OpenAIProxyEmbedding:
    documentation: ""
  OpenAIEmbeddings:
    documentation: "https://python.langchain.com/docs/modules/data_connection/text_embedding/integrations/openai"
  HuggingFaceEmbeddings:
    documentation: "https://python.langchain.com/docs/modules/data_connection/text_embedding/integrations/sentence_transformers"
  CohereEmbeddings:
    documentation: "https://python.langchain.com/docs/modules/data_connection/text_embedding/integrations/cohere"
  WenxinEmbeddings:
    documentation: ""
  HostEmbeddings:
    documentation: ""
  CustomHostEmbedding:
    documentation: ""
llms:
  BishengLLM:
    documentation: ""
  AzureChatOpenAI:
    documentation: ""
  OpenAI:
    documentation: "https://python.langchain.com/docs/modules/model_io/models/llms/integrations/openai"
  ChatOpenAI:
    documentation: "https://python.langchain.com/docs/modules/model_io/models/chat/integrations/openai"
  LlamaCpp:
    documentation: "https://python.langchain.com/docs/modules/model_io/models/llms/integrations/llamacpp"
  CTransformers:
    documentation: "https://python.langchain.com/docs/modules/model_io/models/llms/integrations/ctransformers"
  Cohere:
    documentation: "https://python.langchain.com/docs/modules/model_io/models/llms/integrations/cohere"
  Anthropic:
    documentation: ""
  ChatAnthropic:
    documentation: "https://python.langchain.com/docs/modules/model_io/models/chat/integrations/anthropic"
  HuggingFaceHub:
    documentation: "https://python.langchain.com/docs/modules/model_io/models/llms/integrations/huggingface_hub"
  VertexAI:
    documentation: "https://python.langchain.com/docs/modules/model_io/models/llms/integrations/google_vertex_ai_palm"
  ProxyChatLLM:
    documentation: ""
  ChatMinimaxAI:
    documentation: ""
  ChatWenxin:
    documentation: ""
  ChatQWen:
    documentation: ""
  ChatZhipuAI:
    documentation: ""
  ChatXunfeiAI:
    documentation: ""
  HostChatGLM:
    documentation: ""
  HostBaichuanChat:
    documentation: ""
  HostLlama2Chat:
    documentation: ""
  HostQwenChat:
    documentation: ""
  HostQwen1_5Chat:
    documentation: ""
  CustomLLMChat:
    documentation: ""
  SenseChat:
    documentation: ""
  HostYuanChat:
    documentation: ""
  HostYiChat:
    documentation: ""
  ###
  # There's a bug in this component deactivating until we get it sorted: _language_models.py", line 804, in send_message
  #     is_blocked=safety_attributes.get("blocked", False),
  # AttributeError: 'list' object has no attribute 'get'
  # ChatVertexAI:
  #   documentation: "https://python.langchain.com/docs/modules/model_io/models/chat/integrations/google_vertex_ai_palm"
  ###
memories:
  # https://github.com/supabase-community/supabase-py/issues/482
  # ZepChatMessageHistory:
  #   documentation: "https://python.langchain.com/docs/modules/memory/integrations/zep_memory"
  ConversationEntityMemory:
    documentation: "https://python.langchain.com/docs/modules/memory/integrations/entity_memory_with_sqlite"
  # https://github.com/hwchase17/langchain/issues/6091
  # SQLiteEntityStore:
  #   documentation: "https://python.langchain.com/docs/modules/memory/integrations/entity_memory_with_sqlite"
  PostgresChatMessageHistory:
    documentation: "https://python.langchain.com/docs/modules/memory/integrations/postgres_chat_message_history"
  ConversationBufferMemory:
    documentation: "https://python.langchain.com/docs/modules/memory/how_to/buffer"
  ConversationSummaryMemory:
    documentation: "https://python.langchain.com/docs/modules/memory/how_to/summary"
  ConversationKGMemory:
    documentation: "https://python.langchain.com/docs/modules/memory/how_to/kg"
  ConversationBufferWindowMemory:
    documentation: "https://python.langchain.com/docs/modules/memory/how_to/buffer_window"
  VectorStoreRetrieverMemory:
    documentation: "https://python.langchain.com/docs/modules/memory/how_to/vectorstore_retriever_memory"
  MongoDBChatMessageHistory:
    documentation: "https://python.langchain.com/docs/modules/memory/integrations/mongodb_chat_message_history"
  ConversationRedisMemory:
    documentation: ""
prompts:
  ChatMessagePromptTemplate:
    documentation: "https://python.langchain.com/docs/modules/model_io/prompts/prompt_templates/msg_prompt_templates"
  HumanMessagePromptTemplate:
    documentation: "https://python.langchain.com/docs/modules/model_io/models/chat/how_to/prompts"
  SystemMessagePromptTemplate:
    documentation: "https://python.langchain.com/docs/modules/model_io/models/chat/how_to/prompts"
  ChatPromptTemplate:
    documentation: "https://python.langchain.com/docs/modules/model_io/models/chat/how_to/prompts"
  PromptTemplate:
    documentation: "https://python.langchain.com/docs/modules/model_io/prompts/prompt_templates/"
  MessagesPlaceholder:
    documentation: ""
textsplitters:
  CharacterTextSplitter:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_transformers/text_splitters/character_text_splitter"
  RecursiveCharacterTextSplitter:
    documentation: "https://python.langchain.com/docs/modules/data_connection/document_transformers/text_splitters/recursive_text_splitter"
toolkits:
  OpenAPIToolkit:
    documentation: ""
  JsonToolkit:
    documentation: ""
  VectorStoreInfo:
    documentation: ""
  VectorStoreRouterToolkit:
    documentation: ""
  VectorStoreToolkit:
    documentation: ""
tools:
  Search:
    documentation: ""
  PAL-MATH:
    documentation: ""
  Calculator:
    documentation: ""
  Serper Search:
    documentation: ""
  Tool:
    documentation: ""
  PythonFunctionTool:
    documentation: ""
  PythonFunction:
    documentation: ""
  JsonSpec:
    documentation: ""
  News API:
    documentation: ""
  TMDB API:
    documentation: ""
  Podcast API:
    documentation: ""
  QuerySQLDataBaseTool:
    documentation: ""
  InfoSQLDatabaseTool:
    documentation: ""
  ListSQLDatabaseTool:
    documentation: ""
  BingSearchRun:
    documentation: ""
  GoogleSearchRun:
    documentation: ""
  GoogleSearchResults:
    documentation: ""
  GoogleSerperRun:
    documentation: ""
  JsonListKeysTool:
    documentation: ""
  JsonGetValueTool:
    documentation: ""
  PythonREPLTool:
    documentation: ""
  PythonAstREPLTool:
    documentation: ""
  RequestsGetTool:
    documentation: ""
  RequestsPostTool:
    documentation: ""
  RequestsPatchTool:
    documentation: ""
  RequestsPutTool:
    documentation: ""
  RequestsDeleteTool:
    documentation: ""
  WikipediaQueryRun:
    documentation: ""
  WolframAlphaQueryRun:
    documentation: ""
utilities:
  BingSearchAPIWrapper:
    documentation: ""
  GoogleSearchAPIWrapper:
    documentation: ""
  GoogleSerperAPIWrapper:
    documentation: ""
  SearxResults:
    documentation: ""
  SearxSearchWrapper:
    documentation: ""
  SerpAPIWrapper:
    documentation: ""
  WikipediaAPIWrapper:
    documentation: ""
  WolframAlphaAPIWrapper:
    documentation: ""
retrievers:
  MixEsVectorRetriever:
    documentation: ""
  MultiQueryRetriever:
    documentation: "https://python.langchain.com/docs/modules/data_connection/retrievers/how_to/MultiQueryRetriever"
  # https://github.com/supabase-community/supabase-py/issues/482
  # ZepRetriever:
  #   documentation: "https://python.langchain.com/docs/modules/data_connection/retrievers/integrations/zep_memorystore"
vectorstores:
  ElasticKeywordsSearch:
    documentation: "http://***************:8030"
  ElasticsearchStore:
    documentation: https://python.langchain.com/docs/integrations/vectorstores/elasticsearch
  Milvus:
    documentation: "http://***************:8030"
  MilvusWithPermissionCheck:
    documentation: "http://***************:8030"
  ElasticsearchWithPermissionCheck:
    documentation: "http://***************:8030"
  Chroma:
    documentation: "https://python.langchain.com/docs/modules/data_connection/vectorstores/integrations/chroma"
  Qdrant:
    documentation: "https://python.langchain.com/docs/modules/data_connection/vectorstores/integrations/qdrant"
  Weaviate:
    documentation: "https://python.langchain.com/docs/modules/data_connection/vectorstores/integrations/weaviate"
  FAISS:
    documentation: "https://python.langchain.com/docs/modules/data_connection/vectorstores/integrations/faiss"
  Pinecone:
    documentation: "https://python.langchain.com/docs/modules/data_connection/vectorstores/integrations/pinecone"
  SupabaseVectorStore:
    documentation: "https://python.langchain.com/docs/modules/data_connection/vectorstores/integrations/supabase"
  MongoDBAtlasVectorSearch:
    documentation: "https://python.langchain.com/docs/modules/data_connection/vectorstores/integrations/mongodb_atlas"
  # Requires docarray >=0.32.0 but langchain-serve requires jina 3.15.2 which doesn't support docarray >=0.32.0
  # DocArrayInMemorySearch:
  #   documentation: "https://python.langchain.com/docs/modules/data_connection/vectorstores/integrations/docarray_in_memory"
wrappers:
  RequestsWrapper:
    documentation: ""
  SQLDatabase:
    documentation: ""
output_parsers:
  StructuredOutputParser:
    documentation: "https://python.langchain.com/docs/modules/model_io/output_parsers/structured"
  ResponseSchema:
    documentation: "https://python.langchain.com/docs/modules/model_io/output_parsers/structured"
  RouterOutputParser:
    documentation: ""

input_output:
  VariableNode:
    documentation: ""
  InputNode:
    documentation: ""
  Output:
    documentation: ""
  InputFileNode:
    documentation: ""
