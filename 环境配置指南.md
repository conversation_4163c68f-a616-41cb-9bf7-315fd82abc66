# Bisheng 项目环境配置指南

## 概述

本文档说明如何正确配置 B<PERSON><PERSON> 项目的开发环境。

## 前提
利用本地的pycharm IDE，打开位于 src 的 backend 文件夹。

## 安装
### 方法一：标准安装流程（推荐）
```bash
# 1. 进入项目后端目录
cd src/backend

# 2. 使用 pip -e 安装 pyproject.toml 中的所有依赖
pip install -e .
```

### 方法二：使用代理安装
如果网络连接有问题，使用代理：

```bash
# Windows PowerShell
cd src/backend
$env:HTTP_PROXY="http://127.0.0.1:7897"; $env:HTTPS_PROXY="http://127.0.0.1:7897"; pip install -e .
```

```bash
# Linux/Mac
cd src/backend
export HTTP_PROXY=http://127.0.0.1:7897
export HTTPS_PROXY=http://127.0.0.1:7897
pip install -e .
```

### pip install -e 详解
#### 什么是 pip install -e
`pip install -e .` 是 pip 的可编辑安装模式：
- `-e` 表示 `--editable`，可编辑模式
- `.` 表示当前目录（包含 pyproject.toml 的目录）

#### 工作原理
1. **读取配置**: pip 自动读取 `pyproject.toml` 文件中的依赖配置
2. **解析依赖**: 从 `[tool.poetry.dependencies]` 部分获取所有依赖包
3. **批量安装**: 自动下载并安装所有指定的依赖包
4. **可编辑链接**: 创建到源代码的符号链接，代码修改立即生效

#### 安装的依赖包
根据 `pyproject.toml`，将安装以下主要依赖：

```toml
[tool.poetry.dependencies]
python = ">=3.9,<3.11"
pydantic = "^1.10.18"          # 数据验证
langchain = "^0.2.16"          # LLM 框架
fastapi = "^0.108.0"           # Web 框架
uvicorn = "^0.22.0"            # ASGI 服务器
# ... 其他 80+ 个依赖包
```

#### 验证安装
```bash
# 测试项目导入
python -c "import bisheng; print('✓ 安装成功')"

# 查看所有已安装的包
pip list
```

## 下一步
然后进入 bisheng 文件夹下的 main.py 文件，直接点击调试。

直到出现：
```bash
INFO:     Started server process [47652]
INFO:     Waiting for application startup.
                                                                                                                    
[2025-08-12 15:35:03.461692] [2025-08-12 15:35:03.461692] [DEBUG process-47652-25032 bisheng.database.service:79] - trace=1 Database and tables created successfully                                                                                                                                        
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:7860 (Press CTRL+C to quit)
```

