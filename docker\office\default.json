{"statsd": {"useMetrics": false, "host": "localhost", "port": "8125", "prefix": "ds."}, "log": {"filePath": "", "options": {"replaceConsole": true}}, "queue": {"type": "rabbitmq", "visibilityTimeout": 300, "retentionPeriod": 900}, "storage": {"name": "storage-fs", "fs": {"folderPath": "", "urlExpires": 900, "secretString": "verysecretstring"}, "region": "", "endpoint": "http://localhost/s3", "bucketName": "cache", "storageFolderName": "files", "urlExpires": 604800, "accessKeyId": "AKID", "secretAccessKey": "SECRET", "sslEnabled": false, "s3ForcePathStyle": true, "externalHost": ""}, "rabbitmq": {"url": "amqp://guest:guest@localhost:5672", "socketOptions": {}, "exchangepubsub": "ds.<PERSON><PERSON>", "queueconverttask": "ds.converttask", "queueconvertresponse": "ds.<PERSON>respo<PERSON>", "exchangeconvertdead": "ds.exchangeconvertdead", "queueconvertdead": "ds.convertdead", "queuedelayed": "ds.delayed"}, "activemq": {"connectOptions": {"port": 5672, "host": "localhost", "name": "admin", "reconnect": false}, "queueconverttask": "ds.converttask", "queueconvertresponse": "ds.<PERSON>respo<PERSON>", "queueconvertdead": "ActiveMQ.DLQ", "queuedelayed": "ds.delayed", "topicpubsub": "ds.<PERSON><PERSON>"}, "dnscache": {"enable": true, "ttl": 300, "cachesize": 1000}, "openpgpjs": {"config": {}, "encrypt": {"passwords": ["verysecretstring"]}, "decrypt": {"passwords": ["verysecretstring"]}}, "wopi": {"enable": false, "host": "", "htmlTemplate": "../../web-apps/apps/api/wopi", "wopiZone": "external-http", "favIconUrlWord": "/web-apps/apps/documenteditor/main/resources/img/favicon.ico", "favIconUrlCell": "/web-apps/apps/spreadsheeteditor/main/resources/img/favicon.ico", "favIconUrlSlide": "/web-apps/apps/presentationeditor/main/resources/img/favicon.ico", "fileInfoBlockList": ["FileUrl"], "wordView": ["pdf", "djvu", "xps", "oxps", "doc", "dotx", "dotm", "dot", "fodt", "ott", "rtf", "mht", "html", "htm", "xml", "epub", "fb2"], "wordEdit": ["docx", "docm", "docxf", "oform", "odt", "txt"], "cellView": ["xls", "xlsb", "xltx", "xltm", "xlt", "fods", "ots"], "cellEdit": ["xlsx", "xlsm", "ods", "csv"], "slideView": ["ppt", "ppsx", "ppsm", "pps", "potx", "potm", "pot", "fodp", "otp"], "slideEdit": ["pptx", "pptm", "odp"], "publicKey": "BgIAAACkAABSU0ExAAgAAAEAAQD/NVqekFNi8X3p6Bvdlaxm0GGuggW5kKfVEQzPGuOkGVrz6DrOMNR+k7Pq8tONY+1NHgS6Z+v3959em78qclVDuQX77Tkml0xMHAQHN4sAHF9iQJS8gOBUKSVKaHD7Z8YXch6F212YSUSc8QphpDSHWVShU7rcUeLQsd/0pkflh5+um4YKEZhm4Mou3vstp5p12NeffyK1WFZF7q4jB7jclAslYKQsP82YY3DcRwu5Tl/+W0ifVcXze0mI7v1reJ12pKn8ifRiq+0q5oJST3TRSrvmjLg9Gt3ozhVIt2HUi3La7Qh40YOAUXm0g/hUq2BepeOp1C7WSvaOFHXe6Hqq", "modulus": "qnro3nUUjvZK1i7UqeOlXmCrVPiDtHlRgIPReAjt2nKL1GG3SBXO6N0aPbiM5rtK0XRPUoLmKu2rYvSJ/Kmkdp14a/3uiEl788VVn0hb/l9OuQtH3HBjmM0/LKRgJQuU3LgHI67uRVZYtSJ/n9fYdZqnLfveLsrgZpgRCoabrp+H5Uem9N+x0OJR3LpToVRZhzSkYQrxnERJmF3bhR5yF8Zn+3BoSiUpVOCAvJRAYl8cAIs3BwQcTEyXJjnt+wW5Q1VyKr+bXp/39+tnugQeTe1jjdPy6rOTftQwzjro81oZpOMazwwR1aeQuQWCrmHQZqyV3Rvo6X3xYlOQnlo1/w==", "exponent": "AQAB", "privateKey": "MIIEowIBAAKCAQEAqnro3nUUjvZK1i7UqeOlXmCrVPiDtHlRgIPReAjt2nKL1GG3SBXO6N0aPbiM5rtK0XRPUoLmKu2rYvSJ/Kmkdp14a/3uiEl788VVn0hb/l9OuQtH3HBjmM0/LKRgJQuU3LgHI67uRVZYtSJ/n9fYdZqnLfveLsrgZpgRCoabrp+H5Uem9N+x0OJR3LpToVRZhzSkYQrxnERJmF3bhR5yF8Zn+3BoSiUpVOCAvJRAYl8cAIs3BwQcTEyXJjnt+wW5Q1VyKr+bXp/39+tnugQeTe1jjdPy6rOTftQwzjro81oZpOMazwwR1aeQuQWCrmHQZqyV3Rvo6X3xYlOQnlo1/wIDAQABAoIBAQCKtUSBs8tNYrGTQTlBHXrwpkDg+u7WSZt5sEcfnkxA39BLtlHU8gGO0E9Ihr8GAL+oWjUsEltJ9GTtN8CJ9lFdPVS8sTiCZR/YQOggmFRZTJyVzMrkXgF7Uwwiu3+KxLiTOZx9eRhfDBlTD8W9fXaegX2i2Xp2ohUhBHthEBLdaZTWFi5Sid/Y0dDzBeP6UIJorZ5D+1ybaeIVHjndpwNsIEUGUxPFLrkeiU8Rm4MJ9ahxfywcP7DjQoPGY9Ge5cBhpxfzERWf732wUD6o3+L9tvOBU00CLVjULbGZKTVE2FJMyXK9jr6Zor9Mkhomp6/8Agkr9rp+TPyelFGYEz8hAoGBAOEc09CrL3eYBkhNEcaMQzxBLvOGpg8kaDX5SaArHfl9+U9yzRqss4ARECanp9HuHfjMQo7iejao0ngDjL7BNMSaH74QlSsPOY2iOm8Qvx8/zb7g4h9r1zLjFZb3mpSA4snRZvvdiZ9ugbuVPmhXnDzRRMg45MibJeeOTJNylofRAoGBAMHfF/WutqKDoX25qZo9m74W4bttOj6oIDk1N4/c6M1Z1v/aptYSE06bkWngj9P46kqjaay4hgMtzyGruc5aojPx5MHHf5bo14+Jv4NzYtR2llrUxO+UJX7aCfUYXI7RC93GUmhpeQ414j7SNAXec58d7e+ETw+6cHiAWO4uOSTPAoGATPq5qDLR4Zi4FUNdn8LZPyKfNqHF6YmupT5hIgd8kZO1jKiaYNPL8jBjkIRmjBBcaXcYD5p85nImvumf2J9jNxPpZOpwyC/Fo5xlVROp97qu1eY7DTmodntXJ6/2SXAlnZQhHmHsrPtyG752f+HtyJJbbgiem8cKWDu+DfHybfECgYBbSLo1WiBwgN4nHqZ3E48jgA6le5azLeKOTTpuKKwNFMIhEkj//t7MYn+jhLL0Mf3PSwZU50Vidc1To1IHkbFSGBGIFHFFEzl8QnXEZS4hr/y3o/teezj0c6HAn8nlDRUzRVBEDXWMdV6kCcGpCccTIrqHzpqTY0vV0UkOTQFnDQKBgAxSEhm/gtCYJIMCBe+KBJT9uECV5xDQopTTjsGOkd4306EN2dyPOIlAfwM6K/0qWisa0Ei5i8TbRRuBeTTdLEYLqXCJ7fj5tdD1begBdSVtHQ2WHqzPJSuImTkFi9NXxd1XUyZFM3y6YQvlssSuL7QSxUIEtZHnrJTt3QDd10dj", "publicKeyOld": "BgIAAACkAABSU0ExAAgAAAEAAQD/NVqekFNi8X3p6Bvdlaxm0GGuggW5kKfVEQzPGuOkGVrz6DrOMNR+k7Pq8tONY+1NHgS6Z+v3959em78qclVDuQX77Tkml0xMHAQHN4sAHF9iQJS8gOBUKSVKaHD7Z8YXch6F212YSUSc8QphpDSHWVShU7rcUeLQsd/0pkflh5+um4YKEZhm4Mou3vstp5p12NeffyK1WFZF7q4jB7jclAslYKQsP82YY3DcRwu5Tl/+W0ifVcXze0mI7v1reJ12pKn8ifRiq+0q5oJST3TRSrvmjLg9Gt3ozhVIt2HUi3La7Qh40YOAUXm0g/hUq2BepeOp1C7WSvaOFHXe6Hqq", "modulusOld": "qnro3nUUjvZK1i7UqeOlXmCrVPiDtHlRgIPReAjt2nKL1GG3SBXO6N0aPbiM5rtK0XRPUoLmKu2rYvSJ/Kmkdp14a/3uiEl788VVn0hb/l9OuQtH3HBjmM0/LKRgJQuU3LgHI67uRVZYtSJ/n9fYdZqnLfveLsrgZpgRCoabrp+H5Uem9N+x0OJR3LpToVRZhzSkYQrxnERJmF3bhR5yF8Zn+3BoSiUpVOCAvJRAYl8cAIs3BwQcTEyXJjnt+wW5Q1VyKr+bXp/39+tnugQeTe1jjdPy6rOTftQwzjro81oZpOMazwwR1aeQuQWCrmHQZqyV3Rvo6X3xYlOQnlo1/w==", "exponentOld": "AQAB", "privateKeyOld": "MIIEowIBAAKCAQEAqnro3nUUjvZK1i7UqeOlXmCrVPiDtHlRgIPReAjt2nKL1GG3SBXO6N0aPbiM5rtK0XRPUoLmKu2rYvSJ/Kmkdp14a/3uiEl788VVn0hb/l9OuQtH3HBjmM0/LKRgJQuU3LgHI67uRVZYtSJ/n9fYdZqnLfveLsrgZpgRCoabrp+H5Uem9N+x0OJR3LpToVRZhzSkYQrxnERJmF3bhR5yF8Zn+3BoSiUpVOCAvJRAYl8cAIs3BwQcTEyXJjnt+wW5Q1VyKr+bXp/39+tnugQeTe1jjdPy6rOTftQwzjro81oZpOMazwwR1aeQuQWCrmHQZqyV3Rvo6X3xYlOQnlo1/wIDAQABAoIBAQCKtUSBs8tNYrGTQTlBHXrwpkDg+u7WSZt5sEcfnkxA39BLtlHU8gGO0E9Ihr8GAL+oWjUsEltJ9GTtN8CJ9lFdPVS8sTiCZR/YQOggmFRZTJyVzMrkXgF7Uwwiu3+KxLiTOZx9eRhfDBlTD8W9fXaegX2i2Xp2ohUhBHthEBLdaZTWFi5Sid/Y0dDzBeP6UIJorZ5D+1ybaeIVHjndpwNsIEUGUxPFLrkeiU8Rm4MJ9ahxfywcP7DjQoPGY9Ge5cBhpxfzERWf732wUD6o3+L9tvOBU00CLVjULbGZKTVE2FJMyXK9jr6Zor9Mkhomp6/8Agkr9rp+TPyelFGYEz8hAoGBAOEc09CrL3eYBkhNEcaMQzxBLvOGpg8kaDX5SaArHfl9+U9yzRqss4ARECanp9HuHfjMQo7iejao0ngDjL7BNMSaH74QlSsPOY2iOm8Qvx8/zb7g4h9r1zLjFZb3mpSA4snRZvvdiZ9ugbuVPmhXnDzRRMg45MibJeeOTJNylofRAoGBAMHfF/WutqKDoX25qZo9m74W4bttOj6oIDk1N4/c6M1Z1v/aptYSE06bkWngj9P46kqjaay4hgMtzyGruc5aojPx5MHHf5bo14+Jv4NzYtR2llrUxO+UJX7aCfUYXI7RC93GUmhpeQ414j7SNAXec58d7e+ETw+6cHiAWO4uOSTPAoGATPq5qDLR4Zi4FUNdn8LZPyKfNqHF6YmupT5hIgd8kZO1jKiaYNPL8jBjkIRmjBBcaXcYD5p85nImvumf2J9jNxPpZOpwyC/Fo5xlVROp97qu1eY7DTmodntXJ6/2SXAlnZQhHmHsrPtyG752f+HtyJJbbgiem8cKWDu+DfHybfECgYBbSLo1WiBwgN4nHqZ3E48jgA6le5azLeKOTTpuKKwNFMIhEkj//t7MYn+jhLL0Mf3PSwZU50Vidc1To1IHkbFSGBGIFHFFEzl8QnXEZS4hr/y3o/teezj0c6HAn8nlDRUzRVBEDXWMdV6kCcGpCccTIrqHzpqTY0vV0UkOTQFnDQKBgAxSEhm/gtCYJIMCBe+KBJT9uECV5xDQopTTjsGOkd4306EN2dyPOIlAfwM6K/0qWisa0Ei5i8TbRRuBeTTdLEYLqXCJ7fj5tdD1begBdSVtHQ2WHqzPJSuImTkFi9NXxd1XUyZFM3y6YQvlssSuL7QSxUIEtZHnrJTt3QDd10dj", "refreshLockInterval": "10m"}, "services": {"CoAuthoring": {"server": {"port": 8000, "workerpercpu": 1, "mode": "development", "limits_tempfile_upload": 104857600, "limits_image_size": 26214400, "limits_image_download_timeout": {"connectionAndInactivity": "2m", "wholeCycle": "2m"}, "callbackRequestTimeout": {"connectionAndInactivity": "10m", "wholeCycle": "10m"}, "healthcheckfilepath": "../public/healthcheck.docx", "savetimeoutdelay": 5000, "edit_singleton": false, "forgottenfiles": "forgotten", "forgottenfilesname": "output", "maxRequestChanges": 20000, "openProtectedFile": true, "editorDataStorage": "editor<PERSON><PERSON><PERSON><PERSON><PERSON>", "assemblyFormatAsOrigin": true, "newFileTemplate": "../../document-templates/new", "downloadFileAllowExt": ["pdf"], "tokenRequiredParams": true}, "requestDefaults": {"headers": {"User-Agent": "Node.js/6.13", "Connection": "Keep-Alive"}, "gzip": true, "rejectUnauthorized": true}, "autoAssembly": {"enable": false, "interval": "5m", "step": "1m"}, "utils": {"utils_common_fontdir": "null", "utils_fonts_search_patterns": "*.ttf;*.ttc;*.otf", "limits_image_types_upload": "jpg;jpeg;jpe;png;gif;bmp"}, "sql": {"type": "postgres", "tableChanges": "doc_changes", "tableResult": "task_result", "dbHost": "localhost", "dbPort": 5432, "dbName": "onlyoffice", "dbUser": "onlyoffice", "dbPass": "onlyoffice", "charset": "utf8", "connectionlimit": 10, "max_allowed_packet": 1048575, "pgPoolExtraOptions": {}}, "redis": {"name": "redis", "prefix": "ds:", "host": "localhost", "port": 6379, "options": {}}, "pubsub": {"maxChanges": 1000}, "expire": {"saveLock": 60, "presence": 300, "locks": 604800, "changeindex": 86400, "lockDoc": 30, "message": 86400, "lastsave": 604800, "forcesave": 604800, "saved": 3600, "documentsCron": "0 */2 * * * *", "files": 30, "filesCron": "00 0/1 * * * *", "filesremovedatonce": 100, "sessionidle": "0", "sessionabsolute": "30d", "sessionclosecommand": "2m", "pemStdTTL": "1h", "pemCheckPeriod": "10m", "updateVersionStatus": "5m", "monthUniqueUsers": "1y"}, "ipfilter": {"rules": [{"address": "*", "allowed": true}], "useforrequest": false, "errorcode": 403}, "request-filtering-agent": {"allowPrivateIPAddress": true, "allowMetaIPAddress": true}, "secret": {"browser": {"string": "secret", "file": "", "tenants": {}}, "inbox": {"string": "secret", "file": "", "tenants": {}}, "outbox": {"string": "secret", "file": ""}, "session": {"string": "secret", "file": ""}}, "token": {"enable": {"browser": false, "request": {"inbox": false, "outbox": false}}, "browser": {"secretFromInbox": true}, "inbox": {"header": "Authorization", "prefix": "Bearer ", "inBody": false}, "outbox": {"header": "Authorization", "prefix": "Bearer ", "algorithm": "HS256", "expires": "5m", "inBody": false, "urlExclusionRegex": ""}, "session": {"algorithm": "HS256", "expires": "30d"}, "verifyOptions": {"clockTolerance": 60}}, "plugins": {"uri": "/sdkjs-plugins", "autostart": []}, "themes": {"uri": "/web-apps/apps/common/main/resources/themes"}, "editor": {"spellcheckerUrl": "", "reconnection": {"attempts": 50, "delay": "2s"}, "websocketMaxPayloadSize": "1.5MB"}, "sockjs": {"sockjs_url": "", "websocket": true}, "callbackBackoffOptions": {"retries": 0, "timeout": {"factor": 2, "minTimeout": 1000, "maxTimeout": **********, "randomize": false}, "httpStatus": "429,500-599"}}}, "license": {"license_file": "", "warning_limit_percents": 70, "packageType": 0}, "FileConverter": {"converter": {"maxDownloadBytes": 104857600, "downloadTimeout": {"connectionAndInactivity": "2m", "wholeCycle": "2m"}, "downloadAttemptMaxCount": 3, "downloadAttemptDelay": 1000, "maxprocesscount": 1, "fontDir": "null", "presentationThemesDir": "null", "x2tPath": "null", "docbuilderPath": "null", "docbuilderAllFontsPath": "null", "docbuilderCoreFontsPath": "", "args": "", "spawnOptions": {}, "errorfiles": "", "streamWriterBufferSize": 8388608, "maxRedeliveredCount": 2, "inputLimits": [{"type": "docx;dotx;docm;dotm", "zip": {"uncompressed": "50MB", "template": "*.xml"}}, {"type": "xlsx;xltx;xlsm;xltm", "zip": {"uncompressed": "300MB", "template": "*.xml"}}, {"type": "pptx;ppsx;potx;pptm;ppsm;potm", "zip": {"uncompressed": "50MB", "template": "*.xml"}}]}}}