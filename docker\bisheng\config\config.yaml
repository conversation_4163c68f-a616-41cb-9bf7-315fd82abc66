# 数据库配置， 当前加密串的密码是1234，
# 密码加密参考 https://dataelem.feishu.cn/wiki/BSCcwKd4Yiot3IkOEC8cxGW7nPc#Gxitd1xEeof1TzxdhINcGS6JnXd
database_url:
  "mysql+pymysql://root:gAAAAABlp4b4c59FeVGF_OQRVf6NOUIGdxq8246EBD-b0hdK_jVKRs1x4PoAn0A6C5S6IiFKmWn0Nm5eBUWu-7jxcqw6TiVjQA==@mysql:3306/bisheng?charset=utf8mb4"

# 缓存配置  redis://[[username]:[password]]@localhost:6379/0
# 普通模式:
redis_url: "redis://redis:6379/1"

# 集群模式或者哨兵模式（只能选其一）:
# redis_url: 
#   mode: "cluster"
#   startup_nodes: 
#     - {"host": "***************", "port": 6002}
#   password: encrypt(gAAAAABlp4b4c59FeVGF_OQRVf6NOUIGdxq8246EBD-b0hdK_jVKRs1x4PoAn0A6C5S6IiFKmWn0Nm5eBUWu-7jxcqw6TiVjQA==)
#   #sentinel
#   mode: "sentinel"
#   sentinel_hosts: [("redis", 6379)]
#   sentinel_master: "mymaster"
#   sentinel_password: encrypt(gAAAAABlp4b4c59FeVGF_OQRVf6NOUIGdxq8246EBD-b0hdK_jVKRs1x4PoAn0A6C5S6IiFKmWn0Nm5eBUWu-7jxcqw6TiVjQA==)
#   db: 1

# celery的broken地址
celery_redis_url: "redis://redis:6379/2"

# 知识库的milvus和es配置  支持使用 !env ${PATH} 填写环境变量的值, 若环境变量不存在则会报错
vector_stores:
  milvus:
    connection_args: !env ${BS_MILVUS_CONNECTION_ARGS}
    is_partition: !env ${BS_MILVUS_IS_PARTITION}
    partition_suffix: !env ${BS_MILVUS_PARTITION_SUFFIX}
  elasticsearch:
    url: !env ${BS_ELASTICSEARCH_URL}
    ssl_verify: !env ${BS_ELASTICSEARCH_SSL_VERIFY}


# 对象存储， 目前只支持minio
object_storage:
  type: minio
  minio:
    schema: !env ${BS_MINIO_SCHEMA}
    cert_check: !env ${BS_MINIO_CERT_CHECK}
    endpoint: !env ${BS_MINIO_ENDPOINT}
    sharepoint: !env ${BS_MINIO_SHAREPOIN}
    access_key: !env ${BS_MINIO_ACCESS_KEY}
    secret_key: !env ${BS_MINIO_SECRET_KEY}

environment:
  env: dev
  uns_support: ['png','jpg','jpeg','bmp','doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'txt', 'md', 'html', 'pdf', 'csv', 'tiff']

# 可根据loguru的文档配置不同 handlers
logger_conf:
  # 默认输出到sys.stdout的日志级别, 大于等于此级别都会输出
  level: DEBUG
  # 默认输出格式
  format: '<level>[{time:YYYY-MM-DD HH:mm:ss.SSSSSS}] [{level.name} process-{process.id}-{thread.id} {name}:{line}]</level> - <level>trace={extra[trace_id]} {message}</level>'
  # 参考loguru.add()中的参数可以配置多个handler
  handlers:
      # 文件路径，支持插入一些系统环境变量，若环境变量不存在则置空。例如 HOSTNAME: 主机名。后端会处理环境变量的替换
    - sink: "/app/data/bisheng.log"
      # 日志级别
      level: INFO
      # 日志格式化函数，extra内支持trace_id
      format: '<level>[{time:YYYY-MM-DD HH:mm:ss.SSSSSS}] [{level.name} process-{process.id}-{thread.id} {name}:{line}]</level> - <level>trace={extra[trace_id]} {message}</level>'
      # 每天的几点进行切割
      rotation: "00:00"
      retention: "3 Days"
      enqueue: ture
    - sink: "/app/data/statistic.log"
      level: INFO
      # 和原生不一样，后端会将配置使用eval()执行转为函数用来过滤特定日志级别。推荐lambda
      filter: "lambda record: record['level'].name == 'INFO' and record['message'].startswith('k=s')"
      format: "[{time:YYYY-MM-DD HH:mm:ss.SSSSSS}]|{level}|BISHENG|{extra[trace_id]}||{process.id}|{thread.id}|||#EX_ERR:POS={name},line {line},ERR=500,EMSG={message}"
      rotation: "00:00"
      retention: "3 Days"
      enqueue: ture
