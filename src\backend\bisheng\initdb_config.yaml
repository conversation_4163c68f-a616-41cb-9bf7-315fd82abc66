knowledges: # 知识库相关配置
  unstructured_api_url: ""  # 非必填，若要开启溯源能力则必填。使用邮智官网提供的测试服务，可以填入地址：https://bisheng.dataelem.com/api/v1/etl4llm/predict，如果在私有环境部署了bisheng-unstructured服务，可以填入地址：http://ip:port/v1/etl4llm/predict。注意：添加地址后，需要手动刷新页面后在知识库中上传文档。

llm_request:
  # 控制技能 LLM 组件模型访问的超时配置, 以下是默认值
  request_timeout: 600
  max_retries: 1

default_operator:
  # 使用免登录链接的方式需要配置，因为免登录链接相当于不知道用户信息，我们系统会自动把这些行为记录到某个用户头上，这里用来配置该用户的id
  user: 1
  enable_guest_access: true # 免登录链接是否可访问

# 密码安全相关配置
password_conf:
  # 密码超过X天必须进行修改, 登录提示重新修改密码。大于0策略才生效
  password_valid_period: 200
  # 登录错误时间窗口,单位分钟。在错误时间窗口内超过最大错误次数会封禁用户，password_valid_period和login_error_time_window都大于0才生效
  login_error_time_window: 5
  # 最大错误次数，超过后会封禁用户，大于0时生效
  max_error_times: 0

system_login_method:
  # 是否允许多点登录
  allow_multi_login: true
  # sso系统登录配置（邮智商业扩展套件功能，开源版无需配置）
  gateway_login: false # 是否开启sso登录
  admin_username: admin # 从 SSO/LDAP 注册的管理员用户名

# 登陆页面是否需要输入验证码，可设置为True或False
use_captcha: True

# 会话窗口底部提示文案
dialog_tips:
  "内容由AI生成，仅供参考！"

env:
  # 聊天窗口快捷搜索功能使用的搜索引擎，默认为百度，可以配置为内部文档搜索
  # dialog_quick_search: http://www.baidu.com/s?wd=
  # 当用户的环境前面的网关，不能在同一个端口上既有http又有socket时，需要这个配置，将两个请求区分开，默认可以不用
  # websocket_url: ***************:3003
  office_url: http://IP:8701 # onlyoffice 组件地址，需要浏览器能直接访问
  # 是否展示前端界面上的github和帮助链接
  show_github_and_help: false
  # 是否开启注册
  enable_registration: true

workflow:
  # 节点运行最大步数
  max_steps: 50
  # 等待用户输入的超时时间，单位分钟
  timeout: 5
