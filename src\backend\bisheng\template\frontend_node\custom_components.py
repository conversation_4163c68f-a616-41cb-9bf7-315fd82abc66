from typing import Optional

from bisheng.template.field.base import Template<PERSON>ield
from bisheng.template.frontend_node.base import FrontendNode
from bisheng.template.template.base import Template

DEFAULT_CUSTOM_COMPONENT_CODE = """from bisheng import CustomComponent
from typing import Optional, List, Dict, Union
from bisheng.field_typing import (
    AgentExecutor,
    BaseChatMemory,
    BaseLanguageModel,
    BaseLLM,
    BaseLoader,
    BaseMemory,
    BaseOutputParser,
    BasePromptTemplate,
    BaseRetriever,
    Callable,
    Chain,
    ChatPromptTemplate,
    Data,
    Document,
    Embeddings,
    NestedDict,
    Object,
    PromptTemplate,
    TextSplitter,
    Tool,
    VectorStore,
)


class Component(CustomComponent):
    display_name: str = "Custom Component"
    description: str = "Create any custom component you want!"

    def build_config(self):
        return {"param": {"display_name": "Parameter"}}

    def build(self, param: Data) -> Data:
        return param

"""


class CustomComponentFrontendNode(FrontendNode):
    _format_template: bool = False
    name: str = 'CustomComponent'
    display_name: Optional[str] = 'CustomComponent'
    beta: bool = True
    template: Template = Template(
        type_name='CustomComponent',
        fields=[
            TemplateField(
                field_type='code',
                required=True,
                placeholder='',
                is_list=False,
                show=True,
                value=DEFAULT_CUSTOM_COMPONENT_CODE,
                name='code',
                advanced=False,
                dynamic=True,
            )
        ],
    )
    description: Optional[str] = None
    base_classes: list[str] = []
